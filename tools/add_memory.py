from collections.abc import Generator
from typing import Any, Dict, List
import json
import httpx
import time
from dify_plugin import Tool
from dify_plugin.entities.tool import ToolInvokeMessage
from .logger_config import get_logger, TimingContext

class Mem0Tool(Tool):
    def _invoke(self, tool_parameters: dict[str, Any]) -> Generator[ToolInvokeMessage, None, None]:
        logger = get_logger("AddMemory")

        # Get API key from credentials
        api_key = self.runtime.credentials["mem0_api_key"]

        # Get API URL from credentials or use default (consistent with provider/mem0.yaml)
        api_url = self.runtime.credentials.get("mem0_api_url", "http://localhost:8000")
        if not api_url or api_url.strip() == "":
            api_url = "http://localhost:8000"
        api_url = api_url.rstrip("/")

        # Format messages
        user_content = tool_parameters["user"]
        assistant_content = tool_parameters["assistant"]
        messages = [
            {"role": "user", "content": user_content},
            {"role": "assistant", "content": assistant_content}
        ]

        # Log request information
        logger.info("Adding memory conversation",
                   user_id=tool_parameters['user_id'],
                   user_preview=user_content[:30] + "..." if len(user_content) > 30 else user_content,
                   assistant_preview=assistant_content[:30] + "..." if len(assistant_content) > 30 else assistant_content)
        
        # Prepare payload
        payload = {
            "messages": messages,
            "user_id": tool_parameters["user_id"]
        }

        # Add optional parameters
        if tool_parameters.get("agent_id"):
            payload["agent_id"] = tool_parameters["agent_id"]

        if tool_parameters.get("run_id"):
            payload["run_id"] = tool_parameters["run_id"]

        # Handle inference parameter (default to True)
        infer = tool_parameters.get("infer", True)
        payload["infer"] = infer

        # Handle metadata
        if tool_parameters.get("metadata"):
            try:
                metadata = json.loads(tool_parameters["metadata"])
                payload["metadata"] = metadata
            except json.JSONDecodeError as e:
                error_message = f"Invalid JSON in metadata: {str(e)}"
                logger.log_validation_error("metadata", error_message, user_id=tool_parameters['user_id'])
                yield self.create_json_message({
                    "status": "error",
                    "error": error_message
                })
                yield self.create_text_message(f"Failed to add memory: {error_message}")
                return

        # Custom instructions support
        if tool_parameters.get("custom_instructions"):
            payload["custom_instructions"] = tool_parameters["custom_instructions"]

        if tool_parameters.get("custom_categories"):
            try:
                if isinstance(tool_parameters["custom_categories"], str):
                    payload["custom_categories"] = json.loads(tool_parameters["custom_categories"])
                else:
                    payload["custom_categories"] = tool_parameters["custom_categories"]
            except json.JSONDecodeError as e:
                error_message = f"Invalid JSON in custom_categories: {str(e)}"
                logger.log_validation_error("custom_categories", error_message, user_id=tool_parameters['user_id'])
                yield self.create_json_message({
                    "status": "error",
                    "error": error_message
                })
                yield self.create_text_message(f"Failed to add memory: {error_message}")
                return

        # Async client configuration
        use_async = tool_parameters.get("use_async_client", False)
        if use_async:
            payload["async_processing"] = True

        # Selective memory parameters
        if tool_parameters.get("memory_priority"):
            payload["priority"] = tool_parameters["memory_priority"]

        if tool_parameters.get("auto_prune"):
            payload["auto_prune"] = tool_parameters["auto_prune"]
        
        # Make direct HTTP request to mem0 API with timing
        with TimingContext(logger, "add_memory", user_id=tool_parameters['user_id']) as timing:
            try:
                logger.log_api_call("POST", f"{api_url}/v1/memories/")

                response = httpx.post(
                    f"{api_url}/v1/memories/",
                    json=payload,
                    headers={"Authorization": f"Token {api_key}"},
                    timeout=30
                )

                logger.log_api_call("POST", f"{api_url}/v1/memories/", response.status_code)
                response.raise_for_status()

                # Parse response
                result = response.json()
                logger.debug("Received API response", response_keys=list(result.keys()) if isinstance(result, dict) else "list")
            
                # Handle different response formats
                memory_ids = []

                # Process different response formats
                if isinstance(result, dict):
                    if "memory_id" in result:
                        # Standard format: {"memory_id": "xxx"}
                        memory_ids.append(result["memory_id"])
                        logger.debug("Processed standard format response", memory_id=result['memory_id'])
                    elif "id" in result:
                        # Alternative format: {"id": "xxx"}
                        memory_ids.append(result["id"])
                        logger.debug("Processed alternative format response", memory_id=result['id'])
                    elif "results" in result and isinstance(result["results"], list):
                        # Results collection format: {"results": [{...}, ...]}
                        for r in result["results"]:
                            if isinstance(r, dict):
                                if "event" in r and r["event"] == "ADD" and "id" in r:
                                    memory_ids.append(r["id"])
                                elif "id" in r:
                                    memory_ids.append(r["id"])
                        logger.debug("Processed results collection format", memory_count=len(memory_ids))
                elif isinstance(result, list):
                    # Direct list format: [{...}, ...]
                    for r in result:
                        if isinstance(r, dict):
                            if "event" in r and r["event"] == "ADD" and "id" in r:
                                memory_ids.append(r["id"])
                            elif "id" in r:
                                memory_ids.append(r["id"])
                    logger.debug("Processed list format", memory_count=len(memory_ids))

                # Log final results
                if memory_ids:
                    logger.info("Successfully added memories",
                               user_id=tool_parameters['user_id'],
                               memory_ids=', '.join(memory_ids),
                               memory_count=len(memory_ids))
                else:
                    logger.warning("No memory IDs extracted from response", user_id=tool_parameters['user_id'])
            
            # Return JSON format
            yield self.create_json_message({
                "status": "success",
                "messages": messages,
                "memory_ids": memory_ids
            })
            
            # Return text format
            text_response = "Memory added successfully\n\n"
            text_response += "Added messages:\n"
            for msg in messages:
                text_response += f"- {msg['role']}: {msg['content']}\n"
            
            if memory_ids:
                text_response += f"\nMemory IDs: {', '.join(memory_ids)}"
            else:
                text_response += "\nNo memory IDs returned. Memory may still have been added."
            
                yield self.create_text_message(text_response)

            except httpx.HTTPStatusError as e:
                error_message = f"HTTP error: {e.response.status_code}"
                try:
                    error_data = e.response.json()
                    if "detail" in error_data:
                        error_message = f"Error: {error_data['detail']}"
                except:
                    pass

                logger.error("HTTP error during memory addition",
                           user_id=tool_parameters['user_id'],
                           status_code=e.response.status_code,
                           error=error_message)

                yield self.create_json_message({
                    "status": "error",
                    "error": error_message
                })

                yield self.create_text_message(f"Failed to add memory: {error_message}")

            except Exception as e:
                error_message = f"Error: {str(e)}"

                logger.error("Unexpected error during memory addition",
                           user_id=tool_parameters['user_id'],
                           error=error_message)

                yield self.create_json_message({
                    "status": "error",
                    "error": error_message
                })

                yield self.create_text_message(f"Failed to add memory: {error_message}")
